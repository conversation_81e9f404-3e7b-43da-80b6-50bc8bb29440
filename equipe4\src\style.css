.btn { font-family: inherit; }
:root {
  --bg: #ffffff;
  --panel: #ffffff;
  --border: #e5e7eb;
  --text: #111827;
  --muted: #4b5563;
  --accent: #02dcde;
  font-family: 'Exo 2', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: var(--text);
  background-color: var(--bg);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a { color: var(--muted); text-decoration: none; }
a:hover { color: #ffffff; }

html, body { height: 100%; }
body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  color: var(--text);
  background: var(--bg);
}

h1 { font-size: 36px; line-height: 1.2; }

button { border-radius: 8px; border: 1px solid var(--border); padding: 10px 14px; background: #151922; color: var(--text); }
button:hover { border-color: var(--accent); }

.card { padding: 12px; background: var(--panel); border: 1px solid var(--border); border-radius: 12px; }

#app { text-align: left; }
.page-head { max-width: 1040px; margin: 0 auto; padding: 16px; display: flex; align-items: center; justify-content: space-between; gap: 12px; }
.page-head .subtitle { color: var(--muted); margin: 4px 0 0; }
.panel { max-width: 1040px; margin: 12px auto; padding: 12px 16px; background: var(--panel); border: 1px solid var(--border); border-radius: 12px; }
.btn { border: 1px solid var(--border); background: #111827; color: #fff; padding: 10px 12px; border-radius: 8px; cursor: pointer; }
.btn:hover { opacity: 0.95; }
.btn:disabled { opacity: 0.5; cursor: not-allowed; }
.toolbar { display: flex; flex-wrap: wrap; gap: 10px; align-items: center; margin: 8px 0 12px; }
.input { padding: 8px 10px; border: 1px solid var(--border); border-radius: 8px; min-width: 220px; }
.select { padding: 8px 10px; border: 1px solid var(--border); border-radius: 8px; }
.sort { display: inline-flex; gap: 8px; align-items: center; }
.mapping { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 10px; margin-top: 10px; }
.map-row { display: grid; gap: 6px; }
.table-wrap { overflow: auto; border: 1px solid var(--border); border-radius: 10px; }
table.data { width: 100%; border-collapse: separate; border-spacing: 0; font-size: 14px; }
table.data thead th { position: sticky; top: 0; background: #f9fafb; border-bottom: 1px solid var(--border); text-align: left; padding: 10px 12px; }
table.data tbody td { padding: 10px 12px; border-bottom: 1px solid var(--border); }
table.data tbody tr:nth-child(even) td { background: #fbfdff; }
.pager { display: flex; align-items: center; justify-content: center; gap: 12px; padding: 12px 0; }
.page-info { color: var(--muted); }

.loading { display: flex; align-items: center; gap: 10px; padding: 16px; }
.spinner {
  width: 18px; height: 18px; border-radius: 50%;
  border: 2px solid #e5e7eb; border-top-color: var(--accent);
  animation: spin 0.8s linear infinite;
}
.loading-text { color: var(--muted); }
@keyframes spin { to { transform: rotate(360deg); } }

